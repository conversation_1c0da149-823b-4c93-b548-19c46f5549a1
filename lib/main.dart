import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:bitacora/application/bitacora_app.dart';
import 'package:bitacora/application/error/error_logger.dart';
import 'package:bitacora/cert.dart';
import 'package:bitacora/util/logger/logger.dart';
import 'package:bitacora/util/workmanager/workmanager_utils.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

const _kSentryDsn =
    'https://<EMAIL>/4507976661336064';

Future<void> main() async {
  await runZonedGuarded<Future<void>>(() async {
    await SentryFlutter.init(
      (options) {
        options.dsn = _kSentryDsn;
        options.experimental.replay.sessionSampleRate = 1.0;
        options.experimental.replay.onErrorSampleRate = 1.0;
      },
      appRunner: () async {
        WidgetsFlutterBinding.ensureInitialized();

        unawaited(SystemChrome.setPreferredOrientations(
            [DeviceOrientation.portraitUp]));
        LoggerUtils().init();

        SecurityContext.defaultContext
            .setTrustedCertificatesBytes(utf8.encode(kLetsEncryptR3));

        await Firebase.initializeApp();
        FlutterError.onError = ErrorLogger().recordFlutterError;
        WorkmanagerUtils().init();

        runApp(SentryWidget(child: const BitacoraApp()));
      },
    );
  }, (exception, stackTrace) async {
    await Future.wait([
      ErrorLogger().recordError(exception, stackTrace),
      Sentry.captureException(exception, stackTrace: stackTrace)
    ]);
  });
}
