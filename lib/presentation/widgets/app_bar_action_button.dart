import 'package:bitacora/presentation/theme/theme_util.dart';
import 'package:flutter/material.dart';

const _kPaddingPx = 12.0;
const _kIconSize = 20.0;

enum AppBarActionButtonType { dynamic, onlyText, onlyIcon, full }

class AppBarActionButton extends StatelessWidget {
  final String text;
  final IconData icon;
  final bool withPaddingRight;
  final AppBarActionButtonType type;
  final VoidCallback? onPressed;

  const AppBarActionButton({
    super.key,
    required this.text,
    required this.icon,
    this.onPressed,
    this.type = AppBarActionButtonType.dynamic,
    this.withPaddingRight = true,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        top: _kPaddingPx,
        bottom: _kPaddingPx,
        right: withPaddingRight ? _kPaddingPx : 0.0,
      ),
      child: FilledButton(
        onPressed: onPressed,
        style: ButtonStyle(
          padding: WidgetStateProperty.all(const EdgeInsets.symmetric(
            horizontal: _kPaddingPx,
            vertical: _kPaddingPx / 2,
          )),
          minimumSize: WidgetStateProperty.all(Size.zero),
        ),
        child: _buildChild(context),
      ),
    );
  }

  Widget _buildChild(BuildContext context) {
    final onPrimary = Theme.of(context).colorScheme.onPrimary;
    final iconWidget = Icon(icon, size: _kIconSize, color: onPrimary);
    final textWidget = Text(text, style: TextStyle(color: onPrimary));

    switch (type) {
      case AppBarActionButtonType.onlyText:
        return textWidget;
      case AppBarActionButtonType.onlyIcon:
        return iconWidget;
      case AppBarActionButtonType.dynamic:
        if (isLargeScreen(context)) {
          return textWidget;
        }
        return iconWidget;
      case AppBarActionButtonType.full:
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            iconWidget,
            const SizedBox(width: 4),
            textWidget,
          ],
        );
    }
  }
}
