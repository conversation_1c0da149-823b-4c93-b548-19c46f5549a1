import 'dart:async';
import 'dart:io';
import 'dart:math';

import 'package:bitacora/analytics/analytics_events.dart';
import 'package:bitacora/analytics/analytics_logger.dart';
import 'package:bitacora/application/app_config.dart';
import 'package:bitacora/application/cache/auth/active_session.dart';
import 'package:bitacora/application/cache/logday/active_log_day.dart';
import 'package:bitacora/application/cache/metadata/active_custom_field_metadata_filter.dart';
import 'package:bitacora/application/cache/organization/active_organization.dart';
import 'package:bitacora/application/cache/project/active_project.dart';
import 'package:bitacora/application/cache/template/template_cache.dart';
import 'package:bitacora/application/gps/gps_pre_heating_handler.dart';
import 'package:bitacora/application/hardcoded_data/quick_action_button/hardcoded_quick_action_button_data.dart';
import 'package:bitacora/application/hook/repository_hook.dart';
import 'package:bitacora/application/location_tracking/widget/location_tracking_monitor_subscription_notifier.dart';
import 'package:bitacora/application/router.dart';
import 'package:bitacora/dev/perf/perf_test_monitor.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/custom_field_metadata/custom_field_metadata.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/project/project.dart';
import 'package:bitacora/domain/user/user.dart';
import 'package:bitacora/presentation/daylog/app_bar/daylog_app_bar.dart';
import 'package:bitacora/presentation/daylog/daylog_list_view.dart';
import 'package:bitacora/presentation/daylog/daylog_scroll_coordinator.dart';
import 'package:bitacora/presentation/daylog/entry_timer/entry_timer_top_snack_bar.dart';
import 'package:bitacora/presentation/daylog/location_tracking/location_tracking_top_snack_bar.dart';
import 'package:bitacora/presentation/daylog/my_first_report/my_first_report_top_snack_bar.dart';
import 'package:bitacora/presentation/daylog/new_organization_notifier/new_organization_notifier_top_snack_bar.dart';
import 'package:bitacora/presentation/daylog/open_entry/open_entries_sliding_panel.dart';
import 'package:bitacora/presentation/daylog/quick_action_buttons/quick_action_buttons.dart';
import 'package:bitacora/presentation/daylog/reports_floating_action_button/daylog_reports_floating_action_button.dart';
import 'package:bitacora/presentation/daylog/ai_floating_action_button/daylog_ai_floating_action_button.dart';
import 'package:bitacora/presentation/daylog/selection/daylog_entry_selection.dart';
import 'package:bitacora/presentation/entry/selection/entry_selection_option_bar.dart';
import 'package:bitacora/presentation/widgets/app_drawer/app_drawer.dart';
import 'package:bitacora/presentation/widgets/floating_action_menu/floating_action_menu.dart';
import 'package:bitacora/presentation/widgets/loading_indicator.dart';
import 'package:bitacora/shared_preferences_keys.dart';
import 'package:bitacora/util/access/access_utils.dart';
import 'package:bitacora/util/awesome_notifications/awesome_notifications_utils.dart';
import 'package:bitacora/util/collection_selection/collection_selection.dart';
import 'package:bitacora/util/date_utils.dart';
import 'package:bitacora/util/fcm/fcm_utils.dart';
import 'package:bitacora/util/route_observer.dart';
import 'package:flutter/material.dart';
import 'package:ios_force_notification_permission/ios_force_notification_permission.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

const kSuperSwipeTimePeriodMs = 500;
const kMinSwipeVelocity = 100;

class DaylogPage extends StatefulWidget {
  const DaylogPage({super.key});

  @override
  State<DaylogPage> createState() => _DaylogPageState();
}

class _DaylogPageState extends State<DaylogPage>
    with RouteAware, WidgetsBindingObserver {
  late final DaylogEntrySelection _entrySelection;
  late final DaylogGpsPreHeatingHandler _gpsPreHeatingHandler;
  final RouteObserver _routeObserver = RouteObserverInjector.get();
  int _lastSwipeTime = 0;
  bool _floatingActionMenuIsShowing = false;
  bool _aifloatingActionButtonEnabled = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    final db = context.read<Repository>();
    _entrySelection =
        DaylogEntrySelection(db, context.read<ActiveOrganization>());

    if (!AppConfig().isTest) {
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        final activeSession = context.read<ActiveSession>();
        if (Platform.isIOS) {
          await _maybeForceIosNotificationPermissionsRequest();
        }
        await AwesomeNotificationsUtils().requestPermissions();
        await FcmUtils().requestPermissions();

        // FIXME: move to active session provider?
        RepositoryHook().hook(db, activeSession.value!);
      });
    }

    final locationTrackingSubscriptionNotifier =
        context.read<LocationTrackingMonitorSubscriptionNotifier>();
    _gpsPreHeatingHandler =
        DaylogGpsPreHeatingHandler(locationTrackingSubscriptionNotifier)
          ..maybeHeat();

    _loadPrefs();
  }

  void _loadPrefs() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _aifloatingActionButtonEnabled =
          prefs.getBool(SharedPreferencesKeys.experimentalModeEnabled) ?? false;
    });
  }

  Future<void> _maybeForceIosNotificationPermissionsRequest() async {
    final prefs = await SharedPreferences.getInstance();
    final needRequest = prefs.getBool(
            SharedPreferencesKeys.needForceIosNotificationPermission) ??
        true;
    if (needRequest) {
      await prefs.setBool(
          SharedPreferencesKeys.needForceIosNotificationPermission, false);
      await IosForceNotificationPermission().request();
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _entrySelection.dispose();
    _routeObserver.unsubscribe(this);
    _gpsPreHeatingHandler.dispose();
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _routeObserver.subscribe(this, ModalRoute.of(context)!);
  }

  @override
  void didPopNext() {
    _gpsPreHeatingHandler.maybeHeat();
  }

  @override
  void didPushNext() {
    _gpsPreHeatingHandler.maybeCancel();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) async {
    if (state == AppLifecycleState.paused) {
      _gpsPreHeatingHandler.maybeCancel();
    }

    if (state == AppLifecycleState.resumed) {
      _gpsPreHeatingHandler.maybeHeat();
    }

    if (state == AppLifecycleState.hidden) {
      _gpsPreHeatingHandler.maybeCancel();
    }
  }

  @override
  Widget build(BuildContext context) {
    final templateCache = context.watch<TemplateCache>();
    if (!templateCache.hasLoaded) {
      return const LoadingIndicatorPage();
    }

    PerfTestMonitor().stop(kPerfTestLogin);

    final db = context.read<Repository>();
    final activeOrg = context.read<ActiveOrganization>();
    final filtersCache = context.read<ActiveCustomFieldMetadataFilter>();
    final appConfig = AppConfig();
    return MultiProvider(
      providers: [
        ChangeNotifierProvider<CollectionSelection<LocalId>>.value(
            value: _entrySelection),
        ChangeNotifierProvider<DaylogScrollCoordinator>(
            create: (_) => DaylogScrollCoordinator()),
      ],
      builder: (context, _) => GestureDetector(
        onHorizontalDragEnd: (details) async {
          if (details.velocity.pixelsPerSecond.dy.abs() >=
                  details.velocity.pixelsPerSecond.dx.abs() ||
              details.primaryVelocity!.abs() < kMinSwipeVelocity) {
            // FIXME: primaryVelocity can be null but not velocity.pixels?
            // Maybe check details.velocity.pixelsPerSecond.dx and compare
            return;
          }
          final direction = details.primaryVelocity! > 0 ? -1 : 1;

          final activeLogDay = context.read<ActiveLogDay>();
          final activeProject = context.read<ActiveProject>();
          if (!activeOrg.hasLoaded ||
              !activeProject.hasLoaded ||
              !activeLogDay.hasLoaded) {
            return;
          }
          final activeUser = context.read<ActiveSession>().value!.user;
          final isGhostEntriesEnabled = appConfig.isGhostEntriesEnabled;

          final nextDay = await _findNextDay(
            activeLogDay.value!,
            activeUser,
            activeOrg.value!,
            activeProject.value,
            filtersCache.value,
            direction,
            isGhostEntriesEnabled,
          );
          await activeLogDay.set(nextDay);
        },
        child: Scaffold(
          drawer: const AppDrawer(),
          appBar: const PreferredSize(
            preferredSize: Size.fromHeight(50.0),
            child: DaylogAppBar(),
          ),
          body: Stack(
            children: [
              const Column(
                children: [
                  EntrySelectionOptionBar(),
                  NewOrganizationNotifierTopSnackbar(),
                  MyFirstReportTopSnackbar(),
                  LocationTrackingTopSnackbar(),
                  EntryTimerTopSnackbar(),
                  Expanded(child: DaylogListView()),
                ],
              ),
              const OpenEntriesSlidingPanel(),
              StreamBuilder(
                stream: db.access.getMutations(),
                builder: (context, _) => Consumer<DaylogScrollCoordinator>(
                  builder: (_, coordinator, child) => Positioned(
                    right: kFloatingActionButtonMargin,
                    left: kFloatingActionButtonMargin,
                    bottom: max(
                      kFloatingActionButtonMargin +
                          MediaQuery.paddingOf(context).bottom,
                      coordinator.bottomHeight -
                          kAssumedFloatingActionButtonHeight / 2 -
                          kOpenEntriesExtraTouchableArea,
                    ),
                    child: child!,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      if (HardcodedQuickActionButtonData.enabledOrganizationIds
                          .contains(activeOrg.value!.remoteId!))
                        QuickActionButtons(
                          floatingActionMenuIsShowing:
                              _floatingActionMenuIsShowing,
                        ),
                      if (!_floatingActionMenuIsShowing) ...[
                        if (_aifloatingActionButtonEnabled) ...[
                          const AiFloatingActionButton(),
                          const SizedBox(height: 6.0),
                          // This SizedBox should be conditional with the AI FAB
                        ],
                        const SizedBox(height: 6.0),
                        const Padding(
                            padding: EdgeInsets.only(right: 4.0),
                            child: ReportsFloatingActionButton()),
                        const SizedBox(height: 6.0),
                      ],
                      FutureBuilder<bool>(
                        future: AccessUtils().hasWritePermission(
                          db,
                          activeOrg.value!,
                        ),
                        builder: (context, snapshot) =>
                            !(snapshot.data ?? false)
                                ? const SizedBox()
                                : FloatingActionMenu(
                                    primaryAction: () =>
                                        _onNewEntryClicked(context),
                                    onIsShowingChanged: (value) {
                                      setState(() {
                                        _floatingActionMenuIsShowing = value;
                                      });
                                    },
                                  ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _onNewEntryClicked(BuildContext context) {
    context.read<AnalyticsLogger>().logEvent(AnalyticsEvent.clickNewEntry);

    Navigator.of(context).pushNamed(kRouteEntryForm);
  }

  Future<LogDay> _findNextDay(
    LogDay currentDay,
    User user,
    Organization organization,
    Project? project,
    CustomFieldMetadata? customFieldMetadata,
    int direction,
    bool isGhostEntriesEnabled,
  ) async {
    final time = DateTime.now().millisecondsSinceEpoch;
    LogDay? nextDay;

    if (time - _lastSwipeTime < kSuperSwipeTimePeriodMs) {
      final db = context.read<Repository>();
      nextDay = await db.entry.findNextLogDay(
        db.context(
          queryScope: db.queryScope(
            userId: user.id,
            orgId: organization.id,
            projectId: project?.id,
            metadataFilter: customFieldMetadata,
          ),
        ),
        currentDay,
        direction,
        isGhostEntriesEnabled,
      );
    }

    nextDay = nextDay ??
        LogDay(getLogDayAfter(
          currentDay.value,
          direction,
        ));

    _lastSwipeTime = time;

    return nextDay;
  }
}
