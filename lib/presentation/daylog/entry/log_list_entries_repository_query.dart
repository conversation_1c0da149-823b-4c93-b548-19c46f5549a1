import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/repository_query.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/entry/entry.dart';

class LogListEntriesRepositoryQuery extends RepositoryQuery<List<Entry>> {
  final LogDay? day;
  final bool isOpenEntries;
  final bool isGhostEntriesEnabled;
  final LocalId? assigneeId;

  const LogListEntriesRepositoryQuery({
    this.day,
    this.isOpenEntries = false,
    this.isGhostEntriesEnabled = false,
    this.assigneeId,
  });

  @override
  Future<List<Entry>> run(RepositoryQueryContext context) => isOpenEntries
      ? context.db.entry.findOpenEntries(context, assigneeId: assigneeId)
      : context.db.entry.findDaylog(context, day!, isGhostEntriesEnabled);

  @override
  Fields fields(Repository db) {
    final project = db.project.fieldsBuilder.name().build();
    final user = db.user.fieldsBuilder.name().email().build();

    final worklog = db.worklog.fieldsBuilder
        .project(project)
        .sublocation()
        .title()
        .quantity()
        .build();

    final inventorylog = db.inventorylog.fieldsBuilder
        .type()
        .destProject(project)
        .sourceProject(project)
        .destSublocation()
        .sourceSublocation()
        .provider()
        .itemName()
        .quantity()
        .reason()
        .build();

    final personnellog = db.personnellog.fieldsBuilder
        .project(project)
        .sublocation()
        .name()
        .minutes()
        .entrance()
        .exit()
        .name()
        .build();

    final customField = db.customField.fieldsBuilder
        .name()
        .type()
        .allowedValues(
            db.customFieldAllowedValue.fieldsBuilder.value().label().build())
        .build();
    final templateBlocks = db.templateBlock.fieldsBuilder
        .customFieldOptions(db.customFieldOptions.fieldsBuilder
            .placeholder()
            .isRequired()
            .customField(db.customField.fieldsBuilder.build())
            .build())
        .role()
        .build();
    final fieldsMetadata = db.customFieldMetadata.fieldsBuilder
        .value()
        .project(project)
        .user(user)
        .customField(customField)
        .allowedValue(db.customFieldAllowedValue.fieldsBuilder.value().build())
        .build();
    final templatelog = db.templatelog.fieldsBuilder
        .template(db.template.fieldsBuilder
            .name()
            .groups(
                db.templateGroup.fieldsBuilder.blocks(templateBlocks).build())
            .build())
        .fieldsMetadata(fieldsMetadata)
        .defaultProject(project)
        .build();

    return db.entry.fieldsBuilder
        .day()
        .comments()
        .worklog(worklog)
        .inventorylog(inventorylog)
        .personnellog(personnellog)
        .progresslog(db.progresslog.fieldsBuilder
            .entry(db.entry.fieldsBuilder
                .comments()
                .worklog(worklog)
                .personnellog(personnellog)
                .inventorylog(inventorylog)
                .openState(db.openState.fieldsBuilder.progressive().build())
                .templatelog(templatelog)
                .build())
            .progress()
            .build())
        .openState(
            db.openState.fieldsBuilder.progress().startDay().endDay().build())
        .templatelog(templatelog)
        .startDate()
        .endDate()
        .startTime()
        .endTime()
        .timerStatus()
        .locationTracking(db.locationTracking.fieldsBuilder.build())
        .build();
  }
}
