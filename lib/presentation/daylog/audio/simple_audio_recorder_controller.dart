import 'dart:async';

import 'package:bitacora/util/file_system.dart';
import 'package:bitacora/util/logger/logger.dart';
import 'package:bitacora/util/record.dart';
import 'package:bitacora/util/storage/storage_utils.dart';
import 'package:bitacora/util/stopwatch.dart';
import 'package:flutter/material.dart';
import 'package:path/path.dart' as path;
import 'package:record/record.dart';

class SimpleAudioRecorderController {
  final AudioRecorder _record = RecordInjector.get();
  final FileSystem _fileSystem = FileSystemInjector.get();
  final Stopwatch _stopwatch = StopwatchInjector.get();

  final ValueNotifier<bool> isRecording = ValueNotifier(false);
  final ValueNotifier<bool> isPermissionDenied = ValueNotifier(false);
  final ValueNotifier<Duration> recordingDuration =
      ValueNotifier(Duration.zero);

  String? _stagingPath;
  Timer? _durationTimer;
  Completer<File>? _recordingCompleter;

  Future<File>? get recordingFile => _recordingCompleter?.future;

  Future<File> startRecording() async {
    logger.i('simple-audio-recorder: startRecording');

    _recordingCompleter = Completer<File>();

    final hasPermission = await _record.hasPermission();
    if (!hasPermission) {
      isPermissionDenied.value = true;
      _recordingCompleter!.completeError('Recording permission denied');
      return _recordingCompleter!.future;
    }

    try {
      _stagingPath = await _buildStagingPath();
      await _record.start(const RecordConfig(), path: _stagingPath!);

      isRecording.value = true;
      _stopwatch.start();

      _durationTimer = Timer.periodic(const Duration(milliseconds: 100), (_) {
        recordingDuration.value = _stopwatch.elapsed;
      });

      return _recordingCompleter!.future;
    } catch (e) {
      logger.e('simple-audio-recorder: Error starting recording: $e');
      _recordingCompleter!.completeError('Failed to start recording: $e');
      return _recordingCompleter!.future;
    }
  }

  Future<void> stopRecording() async {
    logger.i('simple-audio-recorder: stopRecording');

    if (!isRecording.value) {
      return;
    }

    try {
      if (await _record.isRecording()) {
        await _record.stop();
      }

      if (_stopwatch.isRunning) {
        _stopwatch.stop();
      }

      _durationTimer?.cancel();
      _durationTimer = null;

      isRecording.value = false;

      if (_stagingPath != null) {
        final audioFile = _fileSystem.file(_stagingPath!);
        if (await audioFile.exists()) {
          if (!(_recordingCompleter?.isCompleted ?? true)) {
            _recordingCompleter!.complete(audioFile);
          }
        } else {
          throw Exception('Recording file not found');
        }
      }
    } catch (e) {
      logger.e('simple-audio-recorder: Error stopping recording: $e');
      if (!(_recordingCompleter?.isCompleted ?? true)) {
        _recordingCompleter!.completeError('Failed to stop recording: $e');
      }
    }
  }

  Future<void> cancelRecording() async {
    logger.i('simple-audio-recorder: cancelRecording');

    try {
      if (await _record.isRecording()) {
        await _record.stop();
      }

      if (_stopwatch.isRunning) {
        _stopwatch.stop();
      }

      _durationTimer?.cancel();
      _durationTimer = null;

      isRecording.value = false;

      if (_stagingPath != null) {
        final file = _fileSystem.file(_stagingPath!);
        if (await file.exists()) {
          await file.delete();
        }
      }

      if (!(_recordingCompleter?.isCompleted ?? true)) {
        _recordingCompleter!.completeError('Recording cancelled');
      }
    } catch (e) {
      logger.e('simple-audio-recorder: Error cancelling recording: $e');
    }
  }

  void dispose() {
    _durationTimer?.cancel();
    _stopwatch.reset();
    isRecording.dispose();
    isPermissionDenied.dispose();
    recordingDuration.dispose();
  }

  Future<String> _buildStagingPath() async {
    logger.i('simple-audio-recorder: _buildStagingPath');
    final key =
        ValueKey<String>(DateTime.now().millisecondsSinceEpoch.toString());
    final stagingDirectory =
        (await StorageUtils().getStagingDirectory(key)).path;
    return path.join(stagingDirectory, 'audio_recording_${key.value}.m4a');
  }
}
