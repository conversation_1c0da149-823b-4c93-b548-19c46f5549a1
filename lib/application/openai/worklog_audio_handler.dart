import 'package:bitacora/application/cache/organization/active_organization.dart';
import 'package:bitacora/application/cache/project/project_cache.dart';
import 'package:bitacora/application/openai/openai_service.dart';
import 'package:bitacora/domain/project/project.dart';

import 'package:bitacora/util/logger/logger.dart';
import 'package:flutter/material.dart';
import 'package:file/file.dart';

class WorklogAudioHandler {
  final ActiveOrganization _activeOrganization;
  final ProjectCache _projectCache;
  final OpenAiService _openAiService;

  final ValueNotifier<bool> isProcessing = ValueNotifier(false);
  final ValueNotifier<bool> isCompleted = ValueNotifier(false);
  final ValueNotifier<String> transcriptionText = ValueNotifier('');

  WorklogAudioHandler(
      this._projectCache, this._openAiService, this._activeOrganization);

  Future<Map<String, dynamic>> processAudioToWorklog(File audioFile) async {
    isProcessing.value = true;
    isCompleted.value = false;

    try {
      final worklogData = await _extractWorklogData(audioFile);
      logger.i('worklog-audio:processAudio Worklog data extracted');

      worklogData['organization_id'] =
          _activeOrganization.value!.remoteId!.apiValue;

      worklogData['extension']['quantity'] =
          worklogData['extension'].containsKey('quantity')
              ? worklogData['extension']['quantity'] * 100
              : null;

      worklogData['worklog_type'] = 1;

      isCompleted.value = true;

      return worklogData;
    } catch (e) {
      logger.e('worklog-audio:processAudio Error: $e');
      rethrow;
    } finally {
      isProcessing.value = false;
    }
  }

  Future<Map<String, dynamic>> _extractWorklogData(File audioFile) async {
    try {
      logger.i('worklog-audio:extract Starting worklog data extraction');

      final projects = _projectCache.value ?? [];

      final worklogData = await _openAiService.createWorklogFromAudio(
        audioFile,
        _formatProjectsList(projects),
      );

      logger.i('worklog-audio:extract Worklog data prepared successfully');

      return worklogData;
    } catch (e) {
      logger.e('worklog-audio:extract Error: $e');
      rethrow;
    }
  }

  List<Map<String, dynamic>> _formatProjectsList(List<Project> projects) {
    return projects
        .where((p) => p.name!.value != '_' && p.name!.value != '?')
        .map((p) => {'name': p.name!.apiValue, 'id': p.remoteId!.apiValue})
        .toList();
  }
}
