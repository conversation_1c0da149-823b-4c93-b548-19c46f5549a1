import 'dart:async';

import 'package:bitacora/util/inject/inject.dart';
import 'package:bitacora/util/logger/logger.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/foundation.dart';

class RemoteConfig {
  static RemoteConfig _instance = RemoteConfig._fromFirebase();

  static const Duration _minimumFetchInterval = Duration(hours: 1);
  static const Duration _fetchTimeout = Duration(minutes: 1);

  factory RemoteConfig() => inject(() => _instance);

  factory RemoteConfig._fromFirebase() {
    try {
      final firebaseRemoteConfig = FirebaseRemoteConfig.instance;

      unawaited(_configureRemoteConfig(firebaseRemoteConfig));

      return RemoteConfig._(firebaseRemoteConfig);
    } catch (e) {
      logger.e('remote-config: Error initializing Firebase Remote Config: $e');
      return RemoteConfig._empty();
    }
  }

  factory RemoteConfig._empty() {
    return RemoteConfig._(MockEmptyRemoteConfig());
  }

  RemoteConfig._(this._firebaseRemoteConfig);

  final FirebaseRemoteConfig _firebaseRemoteConfig;

  static Future<void> _configureRemoteConfig(
      FirebaseRemoteConfig firebaseRemoteConfig) async {
    try {
      await firebaseRemoteConfig.setConfigSettings(RemoteConfigSettings(
        fetchTimeout: _fetchTimeout,
        minimumFetchInterval: _minimumFetchInterval,
      ));

      await firebaseRemoteConfig.fetchAndActivate();
      logger.i('remote-config: Remote configuration successfully initialized');
    } catch (e) {
      logger.e('remote-config: Error configuring Remote Config: $e');
    }
  }

  Future<void> refresh() async {
    try {
      final bool updated = await _firebaseRemoteConfig.fetchAndActivate();
      if (updated) {
        logger.i('remote-config: Remote configuration updated');
      } else {
        logger.i('remote-config: No changes in remote configuration');
      }
    } catch (e) {
      logger.e('remote-config: Error updating remote configuration: $e');
    }
  }

  String get getWorklogPrompt =>
      _firebaseRemoteConfig.getString('worklogPrompt');

  bool getBool(String key) => _firebaseRemoteConfig.getBool(key);

  int getInt(String key) => _firebaseRemoteConfig.getInt(key);

  double getDouble(String key) => _firebaseRemoteConfig.getDouble(key);

  String getString(String key) => _firebaseRemoteConfig.getString(key);

  @visibleForTesting
  static void reset() {
    _instance = RemoteConfig._fromFirebase();
  }
}

class MockEmptyRemoteConfig implements FirebaseRemoteConfig {
  @override
  bool getBool(String key) => false;

  @override
  double getDouble(String key) => 0.0;

  @override
  int getInt(String key) => 0;

  @override
  String getString(String key) => '';

  @override
  Future<bool> fetchAndActivate() async => true;

  @override
  Future<void> setConfigSettings(RemoteConfigSettings settings) async {}

  @override
  dynamic noSuchMethod(Invocation invocation) => super.noSuchMethod(invocation);
}
