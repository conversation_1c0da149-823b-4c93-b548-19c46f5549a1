import 'dart:io';

import 'package:bitacora/util/inject/inject.dart';
import 'package:bitacora/util/platform_utils.dart';
import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';

enum ApiMode {
  staging('development'),
  prod('production');

  final String apiValue;

  const ApiMode(this.apiValue);
}

class AppConfig {
  static AppConfig _instance = AppConfig._fromEnvironment();

  final bool isTest;
  final bool isIntegrationTest;
  final String appName;
  final String targetName;
  final ApiMode apiMode;
  final String webAppUrl;
  final String apiBaseUrl;
  final String locationTrackingApiUrl;
  final String openAiApiUrl;
  final bool isDevToolsEnabled;
  final bool shouldShowPackageInfo;
  final Level logLevel;
  final bool isBackgroundSyncEnabled;
  final bool isGhostEntriesEnabled;
  final bool isReportsEnabled;
  final bool isHybridReportsEnabled;
  final bool isSpeechToTextInAudioRecorderEnabled;
  final bool isInventorylogTypeEditable;
  final String devLoginHackUserPass;
  final String youtubeChannelId;
  final String whatsAppContactNumber;
  final String emailContactAddress;

  factory AppConfig() => inject(() => _instance);

  factory AppConfig._fromEnvironment() {
    const isDev = bool.fromEnvironment('DEV');
    final isTest = Platform.environment.containsKey('FLUTTER_TEST');
    const isIntegrationTest = bool.fromEnvironment('INTEGRATION_TEST');
    const apiMode = ApiMode.staging;

    return AppConfig.fromParams(
      isTest: isTest,
      isIntegrationTest: isIntegrationTest,
      appName: const String.fromEnvironment('APP_NAME'),
      targetName: PlatformUtils().isIOS
          ? (isDev ? 'dev4ios' : 'bit4ios')
          : (isDev ? 'dev4a' : 'bit4a'),
      apiMode: apiMode,
      webAppUrl: 'https://app.bitacora.io',
      apiBaseUrl: apiMode == ApiMode.prod
          ? 'https://api.bitacora.io/api/'
          : 'https://app-dev.bitacora.io/api/',
      locationTrackingApiUrl: apiMode == ApiMode.prod
          ? 'https://gps-tracking.bitacora.io'
          : 'https://gps-tracking-staging.bitacora.io',
      openAiApiUrl: 'https://ai.bitacora.io/',
      isDevToolsEnabled: isDev,
      shouldShowPackageInfo: isDev,
      logLevel: isTest
          ? Level.off
          : isIntegrationTest
              ? Level.info
              : kReleaseMode
                  ? Level.info
                  : Level.debug,
      isBackgroundSyncEnabled: true,
      isGhostEntriesEnabled: false,
      isReportsEnabled: true,
      isHybridReportsEnabled: true,
      isSpeechToTextInAudioRecorderEnabled: PlatformUtils().isIOS,
      isInventorylogTypeEditable: false,
      youtubeChannelId: 'UCVOhZR1hroD0fDIchyuVRfg',
      whatsAppContactNumber: '+528184487740',
      emailContactAddress: '<EMAIL>',
      devLoginHackUserPass: // Choose your test user:
          !isDev ? '' : '<EMAIL>|123456789', // free
      // '<EMAIL>|123456789', // pro
      // '<EMAIL>|123456789', // pro member
      // '<EMAIL>|123456789', // pro staging
      // '<EMAIL>|123456789', // pro staging member
      // '<EMAIL>|12345678', // admin (many orgs / projects)
      // '<EMAIL>|123456789', // marco (free/pro/advanced orgs)
    );
  }

  @visibleForTesting
  AppConfig.fromParams({
    required this.isTest,
    required this.isIntegrationTest,
    required this.appName,
    required this.targetName,
    required this.apiMode,
    required this.webAppUrl,
    required this.apiBaseUrl,
    required this.locationTrackingApiUrl,
    required this.openAiApiUrl,
    required this.isDevToolsEnabled,
    required this.shouldShowPackageInfo,
    required this.logLevel,
    required this.isBackgroundSyncEnabled,
    required this.isGhostEntriesEnabled,
    required this.isReportsEnabled,
    required this.isHybridReportsEnabled,
    required this.isSpeechToTextInAudioRecorderEnabled,
    required this.isInventorylogTypeEditable,
    required this.devLoginHackUserPass,
    required this.youtubeChannelId,
    required this.whatsAppContactNumber,
    required this.emailContactAddress,
  });

  @visibleForTesting
  static void reset() {
    _instance = AppConfig._fromEnvironment();
  }
}
