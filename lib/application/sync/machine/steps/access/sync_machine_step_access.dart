import 'dart:async';

import 'package:bitacora/application/api/api_helper.dart';
import 'package:bitacora/application/sync/machine/sync_machine_step.dart';
import 'package:bitacora/domain/access/access.dart';
import 'package:bitacora/util/logger/logger.dart';

class SyncMachineStepAccess extends SyncMachineStep {
  SyncMachineStepAccess(super.params);

  @override
  Future<void> performSync() async {
    final response = await apiHelper.get('v2/access');
    logger.i(wrapSyncLog(stringifyStatusResponse(response)));
    final accessesData = response.data['accesses'] as List<dynamic>;
    final accessApiList = accessesData
        .map<Access>((e) => apiTranslator.access.fromMap(e))
        .toList(growable: false);

    await db.access.replace(db.context(), accessApiList);
  }

  @override
  String get debugName => 'access';
}
