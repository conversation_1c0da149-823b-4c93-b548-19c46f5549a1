import 'package:bitacora/application/cache/logday/active_log_day.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/l10n/app_localizations_resolver.dart';
import 'package:bitacora/presentation/widgets/entries_heat_map_calendar/entries_heat_map_calendar.dart';
import 'package:bitacora/util/clock.dart';
import 'package:bitacora/util/parent_builder/parent_builder.dart';
import 'package:flutter/material.dart';
import 'package:bitacora/l10n/app_localizations.dart';
import 'package:heatmap_calendar/heatmap_calendar.dart';
import 'package:intl/intl.dart';
import 'package:omni_datetime_picker/omni_datetime_picker.dart';
import 'package:provider/provider.dart';

final DateFormat kDateTimeDisplayDateFormat =
    DateFormat.yMMMd(AppLocalizationsResolver.get().localeName).add_jm();

int getLogDayFromDateTime(DateTime dateTime) {
  return dateTime.year * 10000 + dateTime.month * 100 + dateTime.day;
}

DateTime getDateTimeFromLogDayValue(int logDay, [LogTime? logTime]) {
  return DateTime(
    logDay ~/ 10000,
    (logDay ~/ 100) % 100,
    logDay % 100,
    logTime?.hour ?? 0,
    logTime?.minute ?? 0,
  );
}

DateTime getDateTimeFromLogDay(LogDay logDay, [LogTime? logTime]) {
  return getDateTimeFromLogDayValue(logDay.value, logTime);
}

int getLogDayForToday() {
  return getLogDayFromDateTime(Clock().now());
}

int getLogDayAfter(int logDay, int days) {
  return getLogDayFromDateTime(
      getDateTimeFromLogDayValue(logDay).add(Duration(days: days)));
}

DateTime getDateTimeFromApi(String s, [bool utc = false]) {
  if (!utc) {
    return DateTime.parse(s);
  }

  return DateFormat("yyyy-MM-dd HH:mm:ss").parse(s, true);
}

DateTime getDateTimeFromApiEpoch(String s) {
  final split = s.split('.');
  if (split.length == 1) {
    return DateTime.fromMicrosecondsSinceEpoch(int.parse(split[0]) * 1000000);
  } else if (split.length == 2) {
    return DateTime.fromMicrosecondsSinceEpoch(int.parse(split[0]) * 1000000 +
        int.parse(split[1].padRight(6, '0').substring(0, 6)));
  }
  throw Exception('Unexpected format. 123456 or 1232456.123456');
}

Future<DateTime> pickDate(
  BuildContext context,
  DateTime? currentValue, {
  bool autoHideOnSelect = true,
  void Function(DateTime)? onDayPicked,
}) async {
  var dayPicked = currentValue ??
      getDateTimeFromLogDay(context.read<ActiveLogDay>().value!);
  final datesRange = context.read<DatesRange?>();

  await showModalBottomSheet(
    isScrollControlled: true,
    backgroundColor: Theme.of(context).canvasColor,
    context: context,
    builder: (context) {
      return SafeArea(
        child: ParentBuilder(
          builder: (BuildContext context, Widget child) {
            if (MediaQuery.orientationOf(context) != Orientation.landscape) {
              return child;
            }

            return Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ConstrainedBox(
                  constraints: const BoxConstraints(
                    maxWidth: 260,
                  ),
                  child: child,
                ),
              ],
            );
          },
          child: EntriesHeatMapCalendar(
            initDate: dayPicked,
            autoHideOnSelect: autoHideOnSelect,
            onDayPicked: (picked) {
              if (onDayPicked != null) {
                onDayPicked(picked);
              }
              dayPicked = picked;
            },
            datesRange: datesRange,
          ),
        ),
      );
    },
  );
  return dayPicked;
}

Future<DateTime?> pickDateTime(
  BuildContext context,
  DateTime? currentValue, {
  bool autoHideOnSelect = true,
  void Function(DateTime)? onDayPicked,
}) {
  return showOmniDateTimePicker(
    context: context,
    initialDate: currentValue,
    firstDate: DateTime(1600).subtract(const Duration(days: 3652)),
    lastDate: DateTime.now().add(const Duration(days: 3652)),
    is24HourMode: MediaQuery.alwaysUse24HourFormatOf(context),
    isShowSeconds: false,
    minutesInterval: 1,
    secondsInterval: 1,
    borderRadius: const BorderRadius.all(Radius.circular(16)),
    constraints: const BoxConstraints(maxWidth: 350, maxHeight: 650),
    transitionBuilder: (context, anim1, anim2, child) {
      return FadeTransition(
        opacity: anim1.drive(Tween(begin: 0, end: 1)),
        child: child,
      );
    },
    transitionDuration: const Duration(milliseconds: 200),
    barrierDismissible: true,
    selectableDayPredicate: (dateTime) {
      if (dateTime == DateTime(2023, 2, 25)) {
        return false;
      } else {
        return true;
      }
    },
  );
}

bool isSameDate(DateTime a, DateTime other) {
  return a.year == other.year && a.month == other.month && a.day == other.day;
}

String dateTimeAgo(BuildContext context, DateTime datetime) {
  final date2 = DateTime.now();
  final difference = date2.difference(datetime);

  if ((difference.inDays / 7).floor() >= 2) {
    final dateFormat = DateFormat('dd/MM/yyyy HH:mm');
    return dateFormat.format(datetime);
  } else if ((difference.inDays / 7).floor() >= 1) {
    return AppLocalizations.of(context)!.lastWeek;
  } else if (difference.inDays >= 2) {
    return AppLocalizations.of(context)!.daysAgo(difference.inDays);
  } else if (difference.inDays >= 1) {
    return AppLocalizations.of(context)!.yesterday;
  } else if (difference.inHours >= 2) {
    return AppLocalizations.of(context)!.hoursAgo(difference.inHours);
  } else if (difference.inHours >= 1) {
    return AppLocalizations.of(context)!.anHourAgo;
  } else if (difference.inMinutes >= 2) {
    return AppLocalizations.of(context)!.minutesAgo(difference.inMinutes);
  } else if (difference.inMinutes >= 1) {
    return AppLocalizations.of(context)!.aMinuteAgo;
  } else if (difference.inSeconds >= 3) {
    return AppLocalizations.of(context)!.secondsAgo(difference.inSeconds);
  } else {
    return AppLocalizations.of(context)!.justNow;
  }
}

String durationLeft(BuildContext context, Duration duration) {
  if (duration.inHours >= 2) {
    return AppLocalizations.of(context)!.hoursLeft(duration.inHours);
  } else if (duration.inHours >= 1) {
    return AppLocalizations.of(context)!.anHourLeft;
  } else if (duration.inMinutes >= 2) {
    return AppLocalizations.of(context)!.minutesLeft(duration.inMinutes);
  } else if (duration.inMinutes >= 1) {
    return AppLocalizations.of(context)!.aMinuteLeft;
  } else if (duration.inSeconds >= 5) {
    return AppLocalizations.of(context)!.secondsLeft(duration.inSeconds);
  } else {
    return AppLocalizations.of(context)!.willStopSoon;
  }
}

String displayDuration(Duration duration) {
  String twoDigits(int n) => n.toString().padLeft(2, "0");
  final days = duration.inDays;
  final hours = twoDigits(duration.inHours.remainder(24));
  final twoDigitMinutes = twoDigits(duration.inMinutes.remainder(60));
  final twoDigitSeconds = twoDigits(duration.inSeconds.remainder(60));
  return "${days < 1 ? '' : '$days ${AppLocalizationsResolver.get().days} '}"
      "$hours"
      ":$twoDigitMinutes"
      ":$twoDigitSeconds";
}

String microSecondsToApi(int micros) {
  return '${micros ~/ 1000000}.${'${micros % 1000000}'.padLeft(6, '0')}';
}

String getWeekdayName(int weekday) {
  final DateTime now = DateTime.now().toLocal();
  final int diff = now.weekday - weekday; // weekday is our 1-7 ISO value
  DateTime updatedDt;
  if (diff > 0) {
    updatedDt = now.subtract(Duration(days: diff));
  } else if (diff == 0) {
    updatedDt = now;
  } else {
    updatedDt = now.add(Duration(days: diff * -1));
  }
  final String weekdayName = DateFormat('EEEE').format(updatedDt);
  return weekdayName;
}
