import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/product/value/product_description.dart';
import 'package:bitacora/domain/product/value/product_expiration_date.dart';
import 'package:bitacora/domain/product/value/product_id.dart';
import 'package:bitacora/domain/product/value/product_mode.dart';
import 'package:bitacora/domain/product/value/product_quantity.dart';
import 'package:bitacora/domain/product/value/product_uuid.dart';
import 'package:bitacora/domain/user/user.dart';

export 'package:bitacora/domain/product/value/product_description.dart';
export 'package:bitacora/domain/product/value/product_expiration_date.dart';
export 'package:bitacora/domain/product/value/product_id.dart';
export 'package:bitacora/domain/product/value/product_mode.dart';
export 'package:bitacora/domain/product/value/product_quantity.dart';
export 'package:bitacora/domain/product/value/product_uuid.dart';

class Product extends Model {
  final ProductId? productId;
  final ProductUuid? productUuid;
  final ProductDescription? productDescription;
  final ProductQuantity? quantity;
  final ProductMode? mode;
  final ProductExpirationDate? expirationDate;

  final Organization? organization;
  final List<User>? users;

  const Product({
    super.id,
    super.remoteId,
    this.productId,
    this.productUuid,
    this.productDescription,
    this.quantity,
    this.mode,
    this.expirationDate,
    this.organization,
    this.users,
  });

  Product copyWith({
    ProductId? productId,
    ProductUuid? productUuid,
    ProductDescription? productDescription,
    ProductQuantity? quantity,
    ProductMode? mode,
    ProductExpirationDate? expirationDate,
    Organization? organization,
    List<User>? users,
  }) {
    return Product(
      id: id,
      remoteId: remoteId,
      productId: productId ?? this.productId,
      productUuid: productUuid ?? this.productUuid,
      productDescription: productDescription ?? this.productDescription,
      quantity: quantity ?? this.quantity,
      mode: mode ?? this.mode,
      expirationDate: expirationDate ?? this.expirationDate,
      organization: organization ?? this.organization,
      users: users ?? this.users,
    );
  }

  @override
  Map<Field, dynamic> get fields => {
        ProductField.id: id,
        ProductField.remoteId: remoteId,
        ProductField.productId: productId,
        ProductField.productUuid: productUuid,
        ProductField.productDescription: productDescription,
        ProductField.quantity: quantity,
        ProductField.mode: mode,
        ProductField.expirationDate: expirationDate,
        ProductField.organization: organization,
        ProductField.users: users,
      };
}

enum ProductField with Field {
  id,
  remoteId,
  productId,
  productUuid,
  productDescription,
  quantity,
  mode,
  expirationDate,
  organization,
  users,
}

const productNestedModelFields = {
  ProductField.organization,
  ProductField.users,
};
