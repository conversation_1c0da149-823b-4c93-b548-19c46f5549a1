import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/common/mutation.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/value_object/common.dart';

abstract class RepositoryTable<T extends Model,
    C extends RepositoryQueryContext, F> {
  Future<LocalId?> save(C context, T model, {bool requestSync = false});

  Future<int> delete(C context, LocalId id, {bool requestSync = false});

  Future<T?> find(C context, LocalId id);

  Future<T?> findByRemoteId(C context, RemoteId remoteId);

  Future<T?> search(C context, String pattern);

  Future<int> count(C context);

  Stream<Mutation<T>> getMutations();

  void markDirty();

  F get fieldsBuilder;
}
