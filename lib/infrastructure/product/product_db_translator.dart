import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/product/product.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_translator.dart';
import 'package:bitacora/infrastructure/product/product_db_contract.dart';

class ProductDbTranslator implements DbTranslator<Product> {
  const ProductDbTranslator();

  @override
  Set<Field> get nestedModelFields => productNestedModelFields;

  @override
  Future<Product> fromDb(DbContext context, Map<String, dynamic> map) async {
    final fields = context.fields!.map;
    return Product(
      id: fields[ProductField.id]?.value(map),
      remoteId: fields[ProductField.remoteId]?.value(map),
      productUuid: fields[ProductField.productUuid]?.value(map),
      productDescription: fields[ProductField.productDescription]?.value(map),
      quantity: fields[ProductField.quantity]?.value(map),
      mode: fields[ProductField.mode]?.value(map),
      expirationDate: fields[ProductField.expirationDate]?.value(map),
      organization:
          await fields[ProductField.organization]?.nested(context, map),
      users: await fields[ProductField.users]?.nested(context, map),
    );
  }

  @override
  Future<Map<String, dynamic>> toDb(DbContext context, Product model) async {
    final map = <String, dynamic>{};
    const contract = ProductDbContract();
    addField(map, contract.id, model.id);
    addField(map, contract.remoteId, model.remoteId);
    addField(map, contract.productUuid, model.productUuid);
    addField(map, contract.productDescription, model.productDescription);
    addField(map, contract.quantity, model.quantity);
    addField(map, contract.mode, model.mode);
    addField(map, contract.expirationDate, model.expirationDate);

    await saveNestedModel<Organization>(context, map, contract.organizationId,
        context.db.organization, model.organization);
    return map;
  }
}
