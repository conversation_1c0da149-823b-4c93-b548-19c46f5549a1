import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/domain/product/product.dart';
import 'package:bitacora/domain/product/product_fields_builder.dart';
import 'package:bitacora/infrastructure/db_field.dart';
import 'package:bitacora/infrastructure/db_fields_builder.dart';
import 'package:bitacora/infrastructure/product/product_db_contract.dart';

class ProductDbFieldsBuilder extends DbFieldsBuilder
    implements ProductFieldsBuilder {
  ProductDbFieldsBuilder() {
    _id();
  }

  ProductDbContract get contract => const ProductDbContract();

  ProductDbFieldsBuilder _id() {
    addField(
      ProductField.id,
      DbField(
        column: contract.id,
        valueBuilder: (v) => LocalId(v),
      ),
    );
    return this;
  }

  @override
  ProductDbFieldsBuilder remoteId() {
    addField(
      ProductField.remoteId,
      DbField(
        column: contract.remoteId,
        valueBuilder: (v) => RemoteId(v),
      ),
    );
    return this;
  }

  @override
  ProductDbFieldsBuilder productUuid() {
    addField(
      ProductField.productUuid,
      DbField(
        column: contract.productUuid,
        valueBuilder: (v) => ProductUuid(v),
      ),
    );
    return this;
  }

  @override
  ProductDbFieldsBuilder productDescription() {
    addField(
      ProductField.productDescription,
      DbField(
        column: contract.productDescription,
        valueBuilder: (v) => ProductDescription(v),
      ),
    );
    return this;
  }

  @override
  ProductDbFieldsBuilder quantity() {
    addField(
      ProductField.quantity,
      DbField(
        column: contract.quantity,
        valueBuilder: (v) => ProductQuantity(v),
      ),
    );
    return this;
  }

  @override
  ProductDbFieldsBuilder mode() {
    addField(
      ProductField.mode,
      DbField(
        column: contract.mode,
        valueBuilder: (v) => ProductMode(v),
      ),
    );
    return this;
  }

  @override
  ProductDbFieldsBuilder expirationDate() {
    addField(
      ProductField.expirationDate,
      DbField(
        column: contract.expirationDate,
        valueBuilder: (v) => v == null
            ? null
            : ProductExpirationDate(DateTime.fromMicrosecondsSinceEpoch(v)),
      ),
    );
    return this;
  }

  @override
  ProductDbFieldsBuilder organization(Fields fields) {
    addField(
      ProductField.organization,
      DbField(
        key: ProductField.organization,
        column: contract.organizationId,
        nestedFields: fields as DbFields,
        nestedBuilder: (nestedContext, value) =>
            nestedContext.db.organization.find(nestedContext, LocalId(value)),
      ),
    );
    return this;
  }

  @override
  ProductDbFieldsBuilder users(Fields fields) {
    addField(
      ProductField.users,
      DbField(
        key: ProductField.users,
        column: contract.id,
        nestedFields: fields as DbFields,
        nestedBuilder: (nestedContext, value) => nestedContext.db.product
            .findUsersByProductId(nestedContext, LocalId(value)),
      ),
    );
    return this;
  }
}
