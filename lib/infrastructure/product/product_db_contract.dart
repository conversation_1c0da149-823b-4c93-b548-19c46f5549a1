import 'package:bitacora/infrastructure/db_contract.dart';
import 'package:bitacora/infrastructure/db_repository.dart';

class ProductDbContract extends DbContract {
  static const String _ = 'pr_';
  static const String _tableName = 'product';

  final String id = '${_}id';
  final String remoteId = '${_}remoteId';
  final String productId = '${_}productId';
  final String productUuid = '${_}productUuid';
  final String productDescription = '${_}productDescription';
  final String quantity = '${_}quantity';
  final String mode = '${_}mode';
  final String expirationDate = '${_}expirationDate';
  final String organizationId = '${_}organizationId';

  const ProductDbContract() : super(_, _tableName);

  @override
  int get initialDbVersion => kDbVersionWithProductTables;

  @override
  String get create => '''
  CREATE TABLE $_tableName (
    $id INTEGER PRIMARY KEY AUTOINCREMENT,
    $remoteId INTEGER UNIQUE,
    $productUuid TEXT NOT NULL,
    $productDescription TEXT,
    $quantity INTEGER NOT NULL,
    $mode TEXT,
    $expirationDate INTEGER,
    $organizationId INTEGER NOT NULL
  )
  ''';
}
