import 'package:bitacora/domain/common/model_translator.dart';
import 'package:bitacora/domain/common/value_object/remote_id.dart';
import 'package:bitacora/domain/product/product.dart';
import 'package:bitacora/domain/user/user.dart';

class ProductApiTranslator implements ModelTranslator<Product> {
  const ProductApiTranslator();

  @override
  Product fromMap(Map<String, dynamic> data) {
    List<User>? users;
    if (data['user_ids'] != null && data['user_ids'] is List) {
      users = (data['user_ids'] as List)
          .map((userId) => User(remoteId: RemoteId(userId)))
          .toList();
    }

    return Product(
      remoteId:
          data['id'] != null ? RemoteId(data['id']) : null,
      productUuid: data['product_uuid'] != null
          ? ProductUuid(data['product_uuid'])
          : null,
      productDescription: data['product_description'] != null
          ? ProductDescription(data['product_description'])
          : null,
      quantity:
          data['quantity'] != null ? ProductQuantity(data['quantity']) : null,
      mode: data['mode'] != null ? ProductMode(data['mode']) : null,
      expirationDate: data['expiration_date'] != null
          ? ProductExpirationDate(DateTime.parse(data['expiration_date']))
          : null,
      users: users,
    );
  }

  @override
  Map<String, dynamic> toMap(Product model) {
    final map = <String, dynamic>{};

    if (model.remoteId?.value != null) {
      map['id'] = model.remoteId!.apiValue;
    }
    if (model.productUuid?.value != null) {
      map['product_uuid'] = model.productUuid!.apiValue;
    }
    if (model.productDescription?.value != null) {
      map['product_description'] = model.productDescription!.apiValue;
    }
    if (model.quantity?.value != null) {
      map['quantity'] = model.quantity!.apiValue;
    }
    if (model.mode?.value != null) {
      map['mode'] = model.mode!.apiValue;
    }
    if (model.expirationDate?.value != null) {
      map['expiration_date'] = model.expirationDate!.value!.toIso8601String();
    }

    // Convert users to user_ids array
    if (model.users != null) {
      map['user_ids'] = model.users!
          .where((user) => user.remoteId?.value != null)
          .map((user) => user.remoteId!.apiValue)
          .toList();
    }

    return map;
  }
}
