import 'package:bitacora/infrastructure/worklog/worklog_vertex_schema.dart';
import 'package:firebase_vertexai/firebase_vertexai.dart';

final entryVertexSchema = Schema.object(properties: {
  'comments': Schema.string(nullable: true),
  'source': Schema.object(properties: {
    'type': Schema.string(nullable: false),
    'metadata': Schema.object(
      properties: {
        'transcription': Schema.string(nullable: true),
      },
      nullable: false,
    ),
  }),
  'metadata': Schema.array(
    items: Schema.object(
      properties: {
        'type': Schema.string(nullable: false),
        'value': Schema.string(nullable: false),
      },
      nullable: false,
    ),
    nullable: false,
  ),
  'extension_type': Schema.string(nullable: false),
  'extension': worklogVertexSchema,
});
