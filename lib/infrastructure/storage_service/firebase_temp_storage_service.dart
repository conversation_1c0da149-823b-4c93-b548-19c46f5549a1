import 'dart:typed_data';

import 'package:bitacora/domain/common/storage_service.dart';
import 'package:firebase_storage/firebase_storage.dart';

const _kFirebaseTempStorageBucket = 'vertex_temp';

class FirebaseTempStorageService extends StorageService {
  final FirebaseStorage _storage = FirebaseStorage.instance;
  late final Reference _ref;

  FirebaseTempStorageService() {
    _ref = _storage.ref(_kFirebaseTempStorageBucket);
  }

  @override
  Future<String> uploadFile(
    Uint8List fileBytes,
    String fileName,
  ) async {
    final imageRef = _ref.child(fileName);
    await imageRef.putData(fileBytes);

    return 'gs://${imageRef.bucket}/${imageRef.fullPath}';
  }

  @override
  Future<bool> deleteFile(String fileId) async {
    await _ref.child(fileId).delete();
    return true;
  }
}
